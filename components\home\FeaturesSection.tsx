'use client'

import React from 'react'
import { Brain, Cpu, GraduationCap } from 'lucide-react'
import { motion } from "framer-motion"
import { useRawTranslations } from '@/hooks/useTranslations'

function FeaturesSection() {
  const rawData = useRawTranslations('home.featuresSection')

  const features = [
    {
      name: rawData.features?.[0]?.name || '',
      description: rawData.features?.[0]?.description || '',
      icon: Brain,
      color: 'from-blue-500 to-blue-600',
      bgGradient: 'from-blue-50 to-indigo-50',
      details: rawData.features?.[0]?.details || [],
      stats: rawData.features?.[0]?.stats || {},
      statsLabels: rawData.features?.[0]?.statsLabels || {},
      benefits: rawData.features?.[0]?.benefits || []
    },
    {
      name: rawData.features?.[1]?.name || '',
      description: rawData.features?.[1]?.description || '',
      icon: Cpu,
      color: 'from-green-500 to-green-600',
      bgGradient: 'from-green-50 to-emerald-50',
      details: rawData.features?.[1]?.details || [],
      stats: rawData.features?.[1]?.stats || {},
      statsLabels: rawData.features?.[1]?.statsLabels || {},
      benefits: rawData.features?.[1]?.benefits || []
    },
    {
      name: rawData.features?.[2]?.name || '',
      description: rawData.features?.[2]?.description || '',
      icon: GraduationCap,
      color: 'from-purple-500 to-purple-600',
      bgGradient: 'from-purple-50 to-violet-50',
      details: rawData.features?.[2]?.details || [],
      stats: rawData.features?.[2]?.stats || {},
      statsLabels: rawData.features?.[2]?.statsLabels || {},
      benefits: rawData.features?.[2]?.benefits || []
    }
  ]

  return (
    <section className="py-32 sm:py-40 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50/40 via-indigo-50/20 to-slate-50" />
      <div className="absolute top-0 left-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(59, 130, 246, 0.06)' }} />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 rounded-full blur-3xl" style={{ background: 'rgba(37, 99, 235, 0.04)' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          className="mx-auto max-w-3xl text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4" style={{ color: 'rgb(59 130 246)' }}>{rawData.sectionTitle || '技术创新'}</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            {rawData.title || '引领行业数字化转型'}
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            {rawData.subtitle || '秉持"技术引领未来，创新驱动发展"的理念，我们致力于将前沿科技转化为实际应用，为客户创造价值。'}
          </p>
        </motion.div>

        <div className="mx-auto mt-20 max-w-2xl sm:mt-24 lg:mt-32 lg:max-w-none">
          <dl className="grid max-w-xl grid-cols-1 gap-x-8 gap-y-20 lg:max-w-none lg:grid-cols-3">
            {features.map((feature, index) => (
              <motion.div
                key={feature.name}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: index * 0.2 }}
                viewport={{ once: true }}
              >
                <FeatureCard feature={feature} />
              </motion.div>
            ))}
          </dl>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection


// Feature Card Component
function FeatureCard({ feature }: { feature: any }) {
  const rawData = useRawTranslations('home.featuresSection')
  return (
    <div className="relative group h-full">
      <div className={`card-modern p-8 h-full hover-glow bg-gradient-to-br ${feature.bgGradient} border-0 shadow-xl group-hover:shadow-2xl transition-all duration-500 group-hover:scale-105`}>
        {/* 图标和标题区域 */}
        <div className="text-center mb-8">
          <div className="relative inline-block mb-6">
            <div className={`flex h-20 w-20 items-center justify-center rounded-3xl shadow-lg bg-gradient-to-br ${feature.color} group-hover:scale-110 transition-transform duration-300`}>
              <feature.icon className="h-10 w-10 text-white" aria-hidden="true" />
            </div>
            <div className="absolute inset-0 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.3)' }} />

            {/* 装饰性光环 */}
            <div className="absolute -inset-4 rounded-full border-2 border-blue-200/30 opacity-0 group-hover:opacity-100 animate-pulse transition-opacity duration-300" />
          </div>

          <h3 className="text-2xl font-bold text-slate-800 mb-3 group-hover:text-gradient-modern transition-all duration-300">
            {feature.name}
          </h3>
        </div>

        {/* 描述 */}
        <p className="text-slate-600 leading-relaxed mb-8 text-center">
          {feature.description}
        </p>

        {/* 核心优势 */}
        <div className="mb-8">
          <h4 className="text-sm font-semibold text-slate-700 mb-4 text-center">{rawData.coreAdvantages || '核心优势'}</h4>
          <div className="grid grid-cols-2 gap-3">
            {feature.benefits.map((benefit: string, index: number) => (
              <motion.div
                key={benefit}
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="flex items-center gap-2 text-sm text-slate-600"
              >
                <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex-shrink-0" />
                <span>{benefit}</span>
              </motion.div>
            ))}
          </div>
        </div>

        {/* 技术特性标签 */}
        <div className="flex flex-wrap gap-2 mb-8 justify-center">
          {feature.details.map((detail: string, index: number) => (
            <span
              key={detail}
              className="inline-flex items-center rounded-full px-3 py-1.5 text-xs font-medium transition-all duration-200 hover-lift"
              style={{
                backgroundColor: 'rgba(59, 130, 246, 0.08)',
                color: 'rgb(59 130 246)',
                animationDelay: `${index * 100}ms`
              }}
            >
              {detail}
            </span>
          ))}
        </div>

        {/* 统计数据 */}
        <div className="grid grid-cols-3 gap-4 pt-6 border-t border-slate-200/50">
          {Object.entries(feature.stats).map(([key, value], index) => (
            <motion.div
              key={key}
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="text-lg font-bold text-blue-600 mb-1">{String(value)}</div>
              <div className="text-xs text-slate-500 capitalize">{feature.statsLabels[key] || key}</div>
            </motion.div>
          ))}
        </div>

        {/* 悬停效果底边 */}
        <div className={`absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 bg-gradient-to-r ${feature.color}`} />

        {/* 背景装饰 */}
        <div className="absolute top-4 right-4 w-24 h-24 rounded-full opacity-5 group-hover:opacity-10 transition-opacity duration-300" style={{ background: `linear-gradient(135deg, ${feature.color.replace('from-', '').replace(' to-', ', ')})` }} />
      </div>
    </div>
  )
}