'use client'
import React from 'react'
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

function StatsSection() {
  const t = useTranslations('home.stats')

  const stats = [
    { label: t('items.0.label'), value: t('items.0.value') },
    { label: t('items.1.label'), value: t('items.1.value') },
    { label: t('items.2.label'), value: t('items.2.value') },
    { label: t('items.3.label'), value: t('items.3.value') }
  ]

  return (
    <motion.div
      className="relative -mt-20 pb-32"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, ease: "easeOut" }}
      viewport={{ once: true }}
    >
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-6xl card-modern shadow-glass-lg p-12" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
          <div className="grid grid-cols-2 gap-12 md:grid-cols-4">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="text-center group hover-lift"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="text-4xl lg:text-5xl font-bold text-gradient-modern mb-3 group-hover:scale-110 transition-transform duration-300">
                  {stat.value}
                </div>
                <div className="text-sm lg:text-base text-slate-600 font-medium">
                  {stat.label}
                </div>
                <div className="mt-2 h-1 w-12 rounded-full mx-auto opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216))' }} />
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </motion.div>
  )
}

export default StatsSection