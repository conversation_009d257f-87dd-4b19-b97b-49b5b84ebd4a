'use client'

import Image from 'next/image'
import React from 'react'
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

function PartnersSection() {
  const t = useTranslations('home.partners')
  return (
    <section className="relative py-32 sm:py-40 overflow-hidden">
      {/* 背景层 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-sky-50/50 to-indigo-50/30" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(37,99,235,0.08)_0%,transparent_50%)]" />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-3xl text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full backdrop-blur-sm border mb-6" style={{ backgroundColor: 'rgba(59, 130, 246, 0.08)', borderColor: 'rgba(59, 130, 246, 0.2)' }}>
            <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: 'rgb(59 130 246)' }} />
            <span className="text-sm font-semibold" style={{ color: 'rgb(59 130 246)' }}>{t('title')}</span>
          </div>
          <h2 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-6">
            {t('subtitle')}
          </h2>
          <p className="text-xl leading-relaxed text-slate-600 max-w-2xl mx-auto">
            {t('description')}
          </p>
        </motion.div>

        {/* 合作伙伴展示 */}
        <div className="mt-20">
          <motion.div
            className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <motion.div
                key={`partner-${i}`}
                className="group relative"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.6, delay: i * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ scale: 1.05 }}
              >
                <div className="relative card-modern p-6 shadow-card-modern hover-glow" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
                  <Image
                    className="relative z-10 w-full h-12 object-contain opacity-70 group-hover:opacity-100 transition-all duration-300 filter grayscale group-hover:grayscale-0"
                    src={`https://picsum.photos/seed/partner${i}/200/80`}
                    alt={`合作伙伴 ${i}`}
                    width={200}
                    height={80}
                  />
                  <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216))' }} />
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </div>
    </section>
  )
}

export default PartnersSection