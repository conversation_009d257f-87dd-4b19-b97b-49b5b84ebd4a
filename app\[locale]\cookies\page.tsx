import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Cookie, Shield, Eye, Clock, Settings, Info } from "lucide-react"
import Link from "next/link"

const cookieTypes = [
  {
    title: "必要性 Cookies",
    description: "这些 Cookie 对网站的正常运行至关重要，不能在我们的系统中关闭。",
    icon: Shield,
    examples: ["会话状态", "购物车", "登录信息"]
  },
  {
    title: "分析性 Cookies",
    description: "帮助我们了解访客如何使用网站，以改进网站体验。",
    icon: Eye,
    examples: ["访问统计", "页面停留时间", "点击行为"]
  },
  {
    title: "功能性 Cookies",
    description: "启用网站的高级功能和个性化设置。",
    icon: Settings,
    examples: ["语言偏好", "主题设置", "位置服务"]
  },
  {
    title: "营销性 Cookies",
    description: "用于跟踪访客以提供相关广告。",
    icon: Clock,
    examples: ["广告偏好", "用户画像", "转化跟踪"]
  }
]

const privacyPoints = [
  {
    title: "数据收集目的",
    content: "我们收集 Cookie 数据的目的是为了提供更好的用户体验，包括：个性化内容、记住您的偏好设置、分析网站使用情况等。"
  },
  {
    title: "数据存储期限",
    content: "不同类型的 Cookie 有不同的存储期限：会话 Cookie 在关闭浏览器后失效，持久性 Cookie 可能存储长达 12 个月。"
  },
  {
    title: "数据共享政策",
    content: "我们不会将您的 Cookie 数据出售给第三方。某些分析数据可能与我们的合作伙伴共享，以改进服务质量。"
  },
  {
    title: "用户控制权限",
    content: "您可以随时通过浏览器设置管理或删除 Cookie。请注意，禁用某些 Cookie 可能会影响网站功能。"
  }
]

export default function Cookies() {
  return (
    <div className="relative isolate py-24 sm:py-32">
      {/* 背景效果 */}
      <div
        className="absolute inset-x-0 top-0 -z-10 h-[1000px] transform-gpu overflow-hidden blur-3xl"
        aria-hidden="true"
      >
        <div
          className="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#ff80b5] to-[#9089fc] opacity-30 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem] animate-pulse"
          style={{
            clipPath:
              'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)',
          }}
        />
      </div>

      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题部分 */}
        <div className="mx-auto max-w-2xl text-center">
          <h1 className="text-3xl font-bold tracking-tight sm:text-4xl bg-gradient-to-r from-primary to-primary/50 bg-clip-text text-transparent mb-4">
            Cookie 政策
          </h1>
          <p className="text-lg leading-8 text-muted-foreground">
            我们使用 Cookie 来改善您的浏览体验。了解我们如何使用 Cookie 以及如何管理您的偏好设置。
          </p>
        </div>

        {/* Cookie 类型卡片 */}
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-6 sm:mt-20 lg:mx-0 lg:max-w-none lg:grid-cols-2 xl:grid-cols-4">
          {cookieTypes.map((type, index) => (
            <Card 
              key={type.title}
              className="group hover:shadow-lg transition-all duration-300 border-0 bg-gradient-to-br from-muted/50 to-muted/30 backdrop-blur-sm"
            >
              <CardContent className="pt-6">
                <div className="flex items-center gap-4 mb-4">
                  <div className="p-2 rounded-lg bg-primary/10 group-hover:scale-110 transition-transform duration-300">
                    <type.icon className="h-5 w-5 text-primary" />
                  </div>
                  <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                    {type.title}
                  </h3>
                </div>
                <p className="text-sm text-muted-foreground mb-4">
                  {type.description}
                </p>
                <div className="space-y-2">
                  {type.examples.map((example, i) => (
                    <div 
                      key={i}
                      className="text-xs text-muted-foreground flex items-center gap-2"
                    >
                      <Cookie className="h-3 w-3 text-primary/60" />
                      <span>{example}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* 隐私政策要点 */}
        <div className="mx-auto max-w-3xl mt-20">
          <h2 className="text-2xl font-semibold mb-8 text-center">隐私保护说明</h2>
          <div className="space-y-6">
            {privacyPoints.map((point, index) => (
              <div 
                key={index}
                className="p-6 rounded-xl bg-gradient-to-br from-muted/50 to-muted/30 backdrop-blur-sm group hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center gap-3 mb-2">
                  <Info className="h-5 w-5 text-primary shrink-0" />
                  <h3 className="font-semibold group-hover:text-primary transition-colors">
                    {point.title}
                  </h3>
                </div>
                <p className="text-sm text-muted-foreground pl-8">
                  {point.content}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA 部分 */}
        <div className="mx-auto max-w-2xl text-center mt-20">
          <p className="text-muted-foreground mb-6">
            如果您继续使用本网站，即表示您同意我们的 Cookie 政策。
          </p>
          <div className="flex justify-center gap-4">
            <Button 
              variant="default"
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70"
            >
              接受所有 Cookies
            </Button>
            <Button 
              variant="outline"
              className="border-primary/20 hover:border-primary/40"
            >
              仅必要 Cookies
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
