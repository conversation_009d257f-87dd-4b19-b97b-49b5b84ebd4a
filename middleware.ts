import { NextRequest, NextResponse } from 'next/server'
import { defaultLocale, isValidLocale } from './lib/i18n'

export function middleware(request: NextRequest) {
  const pathname = request.nextUrl.pathname

  // 检查路径是否已经包含语言前缀
  const pathnameHasLocale = pathname.startsWith('/zh') || pathname.startsWith('/en')

  // 如果是根路径，重定向到默认语言
  if (pathname === '/') {
    return NextResponse.redirect(new URL(`/${defaultLocale}`, request.url))
  }

  // 如果路径没有语言前缀，添加默认语言前缀
  if (!pathnameHasLocale) {
    return NextResponse.redirect(new URL(`/${defaultLocale}${pathname}`, request.url))
  }

  // 检查语言前缀是否有效
  const locale = pathname.split('/')[1]
  if (!isValidLocale(locale)) {
    return NextResponse.redirect(new URL(`/${defaultLocale}${pathname.slice(3)}`, request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    // 匹配所有路径，除了 API 路由、静态文件等
    '/((?!api|_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
}
