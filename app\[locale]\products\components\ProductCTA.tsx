'use client'

import { motion } from "framer-motion"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowRight, MessageCircle, Calendar, Phone } from "lucide-react"
import Link from "next/link"
import { useTranslations } from "@/hooks/useTranslations"

export function ProductCTA() {
  const t = useTranslations('productsPage.cta')
  const tCommon = useTranslations('common')
  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900" />
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.3)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(37,99,235,0.2)_0%,transparent_50%)]" />
      
      {/* 浮动元素 */}
      <div className="absolute top-20 left-10 w-64 h-64 rounded-full blur-3xl animate-float opacity-20" style={{ background: 'rgba(59, 130, 246, 0.4)' }} />
      <div className="absolute bottom-20 right-10 w-80 h-80 rounded-full blur-3xl animate-float opacity-15" style={{ background: 'rgba(37, 99, 235, 0.3)', animationDelay: '3s' }} />

      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          {/* 主标题 */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-white mb-6">
              {t('title')}
              <span className="block text-gradient-modern">{t('titleHighlight')}</span>
            </h2>
            <p className="text-xl leading-relaxed text-blue-100 max-w-3xl mx-auto mb-12">
              {t('subtitle')}
            </p>
          </motion.div>

          {/* CTA按钮组 */}
          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-6 mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <Button asChild className="btn-modern shadow-button-modern group text-lg px-8 py-4">
              <Link href="/contact-us">
                {t('contactNow')}
                <ArrowRight className="ml-2 h-5 w-5 transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              className="glass px-8 py-4 text-lg bg-white/10 backdrop-blur-sm border-white/20 hover:bg-white/20 text-white hover:text-white rounded-xl transition-all duration-300 hover-lift"
            >
              <Calendar className="mr-2 h-5 w-5" />
              {t('bookDemo')}
            </Button>
          </motion.div>

          {/* 联系方式 */}
          <motion.div
            className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            viewport={{ once: true }}
          >
            {[
              {
                icon: MessageCircle,
                title: t('contact.online.title'),
                description: t('contact.online.description'),
                action: t('contact.online.action')
              },
              {
                icon: Phone,
                title: t('contact.phone.title'),
                description: t('contact.phone.description'),
                action: t('contact.phone.action')
              },
              {
                icon: Calendar,
                title: t('contact.meeting.title'),
                description: t('contact.meeting.description'),
                action: t('contact.meeting.action')
              }
            ].map((item, index) => (
              <div key={index} className="group">
                <div className="p-6 rounded-2xl bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/15 transition-all duration-300 hover-lift text-center">
                  <div className="mx-auto w-12 h-12 mb-4 flex items-center justify-center rounded-xl bg-white/20 group-hover:bg-white/30 transition-colors duration-300">
                    <item.icon className="w-6 h-6 text-white" />
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">{item.title}</h3>
                  <p className="text-blue-100 text-sm mb-4">{item.description}</p>
                  <button className="text-sm font-medium text-blue-300 hover:text-white transition-colors duration-300">
                    {item.action} →
                  </button>
                </div>
              </div>
            ))}
          </motion.div>

          {/* 底部信任标识 */}
          <motion.div
            className="mt-16 pt-8 border-t border-white/20"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            viewport={{ once: true }}
          >
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-blue-100">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('trust.iso')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('trust.soc2')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('trust.gdpr')}</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 rounded-full bg-green-400"></div>
                <span className="text-sm">{t('trust.support')}</span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
