'use client'

import React from 'react'
import Link from "next/link"
import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { useTranslations } from '@/hooks/useTranslations'

function CTASection() {
  const t = useTranslations('home.cta')
  return (
    <div className="relative isolate mt-32 px-6 py-32 sm:mt-40 sm:py-40 lg:px-8">
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.3)_0%,transparent_50%),radial-gradient(circle_at_70%_80%,rgba(37,99,235,0.2)_0%,transparent_50%)]" />
      </div>

      <div className="mx-auto max-w-2xl text-center">
        <motion.h2
          className="text-3xl font-bold tracking-tight sm:text-4xl text-white"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          {t('title')}
        </motion.h2>
        <motion.p
          className="mx-auto mt-6 max-w-xl text-lg leading-8 text-blue-100"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {t('description')}
        </motion.p>
        <motion.div
          className="mt-10 flex items-center justify-center gap-x-6"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <Button asChild className="btn-modern shadow-button-modern">
            <Link href="/contact-us">{t('buttons.consultation')}</Link>
          </Button>
          <Button className="glass px-8 py-3 text-lg bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-white hover:text-white rounded-xl transition-all duration-300 hover-lift">
            {t('buttons.viewCases')}
          </Button>
        </motion.div>
      </div>
    </div>
  )
}

export default CTASection