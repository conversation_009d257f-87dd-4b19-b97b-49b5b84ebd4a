import { NextResponse } from 'next/server'
import {
  Brain, Cpu, GraduationCap, Target, Eye, Database,
  LineChart, Zap, Globe, CheckCircle2, Shield,
  Users, BookOpen, Award, BarChart, Clock, Settings
} from "lucide-react"
import { getProducts } from "@/lib/products-data"

// Create a simple translation function for API
const createApiTranslation = (key: string) => {
  // For API, we'll use the Chinese keys as fallback since it's the original language
  const translations: Record<string, string> = {
    'products.aiAnnotation.name': 'AI智能标注平台',
    'products.cpuRental.name': 'CPU算力租用',
    'products.educationManagement.name': '教育培训管理系统'
    // Add more translations as needed
  }
  return translations[key] || key
}

const getProductsData = () => getProducts(createApiTranslation)

// Remove the hardcoded products array - now using getProducts function

export async function GET() {
  const products = getProductsData()
  return NextResponse.json(products)
}