# 零点科技网站快速启动指南

## 🚀 快速开始

### 1. 环境准备
确保您的系统已安装：
- Node.js 18+ 
- npm 或 yarn

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制环境变量示例文件：
```bash
cp .env.example .env.local
```

编辑 `.env.local` 文件，配置必要的环境变量：
```env
# 网站基础配置
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://api.your-domain.com

# SEO 和分析（可选）
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_BAIDU_ID=xxxxxxxxxxxxxxxx

# 搜索引擎验证（可选）
NEXT_PUBLIC_GOOGLE_VERIFICATION=your-verification-code
NEXT_PUBLIC_BAIDU_VERIFICATION=your-verification-code
NEXT_PUBLIC_BING_VERIFICATION=your-verification-code
```

### 4. 启动开发服务器
```bash
npm run dev
```

访问 http://localhost:3000 查看网站。

### 5. 构建生产版本
```bash
npm run build
npm run start
```

## 📁 项目结构

```
├── app/                    # Next.js 13 App Router
│   ├── layout.tsx         # 根布局
│   ├── page.tsx           # 首页
│   ├── sitemap.ts         # 自动生成 sitemap
│   ├── robots.ts          # 自动生成 robots.txt
│   ├── products/          # 产品页面
│   ├── news/              # 新闻页面
│   └── ...
├── components/            # 可复用组件
│   ├── ui/               # UI 基础组件
│   ├── home/             # 首页专用组件
│   ├── Breadcrumb.tsx    # 面包屑导航
│   ├── StructuredData.tsx # 结构化数据
│   └── ...
├── lib/                  # 工具函数和配置
│   ├── seo-utils.ts      # SEO 工具函数
│   ├── analytics.ts      # 分析和监控
│   └── ...
└── public/               # 静态资源
    ├── manifest.json     # PWA 配置
    └── ...
```

## 🔧 主要功能

### SSR (服务器端渲染)
- ✅ 完整的 SSR 支持
- ✅ 动态路由预渲染
- ✅ 优化的代码分割

### SEO 优化
- ✅ 动态 metadata 生成
- ✅ 自动 sitemap 生成
- ✅ 结构化数据标记
- ✅ Open Graph 优化
- ✅ 面包屑导航

### 性能优化
- ✅ 图片优化
- ✅ 代码分割
- ✅ 缓存策略
- ✅ Web Vitals 监控

## 🛠️ 开发指南

### 添加新页面
1. 在 `app/` 目录下创建新的路由文件夹
2. 添加 `page.tsx` 文件
3. 配置 metadata 和 SEO 优化
4. 更新 sitemap.ts（如果需要）

### 自定义 SEO
使用 `lib/seo-utils.ts` 中的工具函数：
```typescript
import { generatePageStructuredData } from '@/lib/seo-utils'

const structuredData = generatePageStructuredData({
  name: "页面标题",
  description: "页面描述",
  url: "/page-url"
})
```

### 添加分析代码
在 `lib/analytics.ts` 中配置您的分析服务。

## 📊 SEO 检查清单

部署前请确保：
- [ ] 所有页面都有独特的 title 和 description
- [ ] 图片都有 alt 标签
- [ ] 内部链接结构合理
- [ ] sitemap.xml 可访问
- [ ] robots.txt 配置正确
- [ ] 结构化数据验证通过

## 🚀 部署

### Vercel 部署
1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### 其他平台
```bash
npm run build
```
将 `.next` 目录部署到您的服务器。

## 📞 支持

如有问题，请联系开发团队或查看：
- [Next.js 文档](https://nextjs.org/docs)
- [SEO 最佳实践](https://developers.google.com/search/docs)

---

**最后更新**: 2024年3月
**版本**: 1.0.0
