"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface News {
  id: string
  title: string
  content: string
  date: string
  category: string
  image: string
  author: {
    name: string
    role: string
    image: string
  }
}

interface NewsDetailProps {
  news: News
}

export function NewsDetail({ news }: NewsDetailProps) {
  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-3xl px-6 lg:px-8">
        <div className="mb-8">
          <Button variant="ghost" asChild>
            <Link href="/news">
              <ArrowLeft className="mr-2 h-4 w-4" />
              返回新闻列表
            </Link>
          </Button>
        </div>
        
        <article>
          <div className="mb-8">
            <div className="flex items-center gap-x-4 text-sm">
              <time dateTime={news.date} className="text-muted-foreground">{news.date}</time>
              <span className="relative z-10 rounded-full bg-primary/10 px-3 py-1.5 font-medium text-primary">
                {news.category}
              </span>
            </div>
            <h1 className="mt-4 text-3xl font-bold tracking-tight sm:text-4xl">
              {news.title}
            </h1>
            <div className="mt-8 flex items-center gap-x-4">
              <Image
                src={news.author.image}
                alt={news.author.name}
                width={40}
                height={40}
                className="h-10 w-10 rounded-full"
              />
              <div className="text-sm leading-6">
                <p className="font-semibold">{news.author.name}</p>
                <p className="text-muted-foreground">{news.author.role}</p>
              </div>
            </div>
          </div>

          <div className="relative w-full mb-8">
            <Image
              src={news.image}
              alt={news.title}
              width={1200}
              height={600}
              className="aspect-[2/1] w-full rounded-2xl object-cover"
            />
          </div>

          <div 
            className="prose prose-lg max-w-none prose-primary"
            dangerouslySetInnerHTML={{ __html: news.content }}
          />
        </article>
      </div>
    </div>
  )
} 