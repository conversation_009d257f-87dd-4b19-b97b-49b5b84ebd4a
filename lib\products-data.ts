"use client"

type TranslationFunction = (key: string) => string;

// Function to get products with translations
export const getProducts = (t: TranslationFunction) => [
  {
    slug: "ai-annotation",
    name: t("products.aiAnnotation.name"),
    description: t("products.aiAnnotation.description"),
    iconName: "Brain",
    features: [
      { text: t("products.aiAnnotation.features.imageAnnotation"), iconName: "Eye" },
      { text: t("products.aiAnnotation.features.textAnnotation"), iconName: "Database" },
      { text: t("products.aiAnnotation.features.audioAnnotation"), iconName: "Target" },
      { text: t("products.aiAnnotation.features.qualityControl"), iconName: "CheckCircle2" }
    ],
    highlight: t("products.aiAnnotation.highlight"),
    price: t("products.aiAnnotation.price"),
    category: t("products.aiAnnotation.category")
  },
  {
    slug: "cpu-rental",
    name: t("products.cpuRental.name"),
    description: t("products.cpuRental.description"),
    iconName: "Cpu",
    features: [
      { text: t("products.cpuRental.features.highPerformance"), iconName: "Zap" },
      { text: t("products.cpuRental.features.elasticScaling"), iconName: "Globe" },
      { text: t("products.cpuRental.features.payAsYouGo"), iconName: "BarChart" },
      { text: t("products.cpuRental.features.monitoring247"), iconName: "Shield" }
    ],
    highlight: t("products.cpuRental.highlight"),
    price: t("products.cpuRental.price"),
    category: t("products.cpuRental.category")
  },
  {
    slug: "education-management",
    name: t("products.educationManagement.name"),
    description: t("products.educationManagement.description"),
    iconName: "GraduationCap",
    features: [
      { text: t("products.educationManagement.features.courseManagement"), iconName: "BookOpen" },
      { text: t("products.educationManagement.features.onlineExam"), iconName: "Target" },
      { text: t("products.educationManagement.features.studentTracking"), iconName: "Users" },
      { text: t("products.educationManagement.features.certificateSystem"), iconName: "Award" }
    ],
    highlight: t("products.educationManagement.highlight"),
    price: t("products.educationManagement.price"),
    category: t("products.educationManagement.category")
  },
  {
    slug: "data-annotation-pro",
    name: t("products.dataAnnotationPro.name"),
    description: t("products.dataAnnotationPro.description"),
    iconName: "Database",
    features: [
      { text: t("products.dataAnnotationPro.features.customSolution"), iconName: "Settings" },
      { text: t("products.dataAnnotationPro.features.professionalQC"), iconName: "CheckCircle2" },
      { text: t("products.dataAnnotationPro.features.batchProcessing"), iconName: "Zap" },
      { text: t("products.dataAnnotationPro.features.apiIntegration"), iconName: "Globe" }
    ],
    price: t("products.dataAnnotationPro.price"),
    category: t("products.dataAnnotationPro.category")
  },
  {
    slug: "custom-web-development",
    name: t("products.customWebDevelopment.name"),
    description: t("products.customWebDevelopment.description"),
    iconName: "Code",
    features: [
      { text: t("products.customWebDevelopment.features.fullStack"), iconName: "Globe" },
      { text: t("products.customWebDevelopment.features.responsiveDesign"), iconName: "Monitor" },
      { text: t("products.customWebDevelopment.features.apiDevelopment"), iconName: "Database" },
      { text: t("products.customWebDevelopment.features.performanceOptimization"), iconName: "Zap" }
    ],
    highlight: t("products.customWebDevelopment.highlight"),
    price: t("products.customWebDevelopment.price"),
    category: t("products.customWebDevelopment.category")
  },
  {
    slug: "mobile-app-development",
    name: t("products.mobileAppDevelopment.name"),
    description: t("products.mobileAppDevelopment.description"),
    iconName: "Smartphone",
    features: [
      { text: t("products.mobileAppDevelopment.features.nativeDevelopment"), iconName: "Smartphone" },
      { text: t("products.mobileAppDevelopment.features.crossPlatform"), iconName: "Globe" },
      { text: t("products.mobileAppDevelopment.features.uiuxDesign"), iconName: "Palette" },
      { text: t("products.mobileAppDevelopment.features.appPublishing"), iconName: "Upload" }
    ],
    price: t("products.mobileAppDevelopment.price"),
    category: t("products.mobileAppDevelopment.category")
  },
  {
    slug: "enterprise-software",
    name: t("products.enterpriseSoftware.name"),
    description: t("products.enterpriseSoftware.description"),
    iconName: "Building",
    features: [
      { text: t("products.enterpriseSoftware.features.requirementAnalysis"), iconName: "Search" },
      { text: t("products.enterpriseSoftware.features.systemDesign"), iconName: "Settings" },
      { text: t("products.enterpriseSoftware.features.integrationDevelopment"), iconName: "Code" },
      { text: t("products.enterpriseSoftware.features.operationSupport"), iconName: "Shield" }
    ],
    price: t("products.enterpriseSoftware.price"),
    category: t("products.enterpriseSoftware.category")
  }
];

// Function to get product details with translations
export const getProductDetails = (t: TranslationFunction) => ({
  "ai-annotation": {
    name: t("productDetails.aiAnnotation.name"),
    description: t("productDetails.aiAnnotation.description"),
    features: [
      t("productDetails.aiAnnotation.features.imageAnnotationService"),
      t("productDetails.aiAnnotation.features.textAnnotationService"),
      t("productDetails.aiAnnotation.features.audioAnnotationService"),
      t("productDetails.aiAnnotation.features.qualityControlSystem"),
      t("productDetails.aiAnnotation.features.batchProcessingCapability"),
      t("productDetails.aiAnnotation.features.apiInterfaceSupport"),
      t("productDetails.aiAnnotation.features.professionalAnnotationTeam"),
      t("productDetails.aiAnnotation.features.multipleQualityInspection"),
      t("productDetails.aiAnnotation.features.dataSecurityProtection")
    ],
    techSpecs: {
      deployment: t("productDetails.aiAnnotation.techSpecs.deployment"),
      security: t("productDetails.aiAnnotation.techSpecs.security"),
      availability: t("productDetails.aiAnnotation.techSpecs.availability"),
      support: t("productDetails.aiAnnotation.techSpecs.support"),
    },
    featureList: [
      {
        title: t("productDetails.aiAnnotation.featureList.imageAnnotation.title"),
        description: t("productDetails.aiAnnotation.featureList.imageAnnotation.description"),
        features: [
          {
            name: t("productDetails.aiAnnotation.featureList.imageAnnotation.features.objectDetection.name"),
            description: t("productDetails.aiAnnotation.featureList.imageAnnotation.features.objectDetection.description"),
            icon: "Target"
          },
          {
            name: "图像分割",
            description: "像素级精确分割，支持语义分割和实例分割",
            icon: "Scissors"
          },
          {
            name: t("productDetails.aiAnnotation.featureList.imageAnnotation.features.keypointAnnotation.name"),
            description: t("productDetails.aiAnnotation.featureList.imageAnnotation.features.keypointAnnotation.description"),
            icon: "MapPin"
          }
        ]
      },
      {
        title: t("productDetails.aiAnnotation.featureList.textAnnotation.title"),
        description: t("productDetails.aiAnnotation.featureList.textAnnotation.description"),
        features: [
          {
            name: t("productDetails.aiAnnotation.featureList.textAnnotation.features.entityRecognition.name"),
            description: t("productDetails.aiAnnotation.featureList.textAnnotation.features.entityRecognition.description"),
            icon: "Tag"
          },
          {
            name: t("productDetails.aiAnnotation.featureList.textAnnotation.features.sentimentAnalysis.name"),
            description: t("productDetails.aiAnnotation.featureList.textAnnotation.features.sentimentAnalysis.description"),
            icon: "Heart"
          },
          {
            name: t("productDetails.aiAnnotation.featureList.textAnnotation.features.relationExtraction.name"),
            description: t("productDetails.aiAnnotation.featureList.textAnnotation.features.relationExtraction.description"),
            icon: "Network"
          }
        ]
      }
    ],
    demoVideo: {
      url: "https://player.vimeo.com/video/824804225",
      thumbnail: "https://images.unsplash.com/photo-1677442136019-21780ecad995?q=80&w=2940&auto=format&fit=crop",
    },
    benefits: [
      {
        title: t("productDetails.aiAnnotation.benefits.improveModelAccuracy.title"),
        description: t("productDetails.aiAnnotation.benefits.improveModelAccuracy.description")
      },
      {
        title: t("productDetails.aiAnnotation.benefits.saveTimeCost.title"),
        description: t("productDetails.aiAnnotation.benefits.saveTimeCost.description")
      },
      {
        title: t("productDetails.aiAnnotation.benefits.ensureDataSecurity.title"),
        description: t("productDetails.aiAnnotation.benefits.ensureDataSecurity.description")
      }
    ]
  }
  // Note: Other product details would follow the same pattern
  // For brevity, only showing ai-annotation as an example
});

// Legacy exports for backward compatibility
export const products = [];
export const productDetails = {};
