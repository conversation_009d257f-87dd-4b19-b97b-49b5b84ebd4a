// Web Vitals 性能监控
export function reportWebVitals(metric: any) {
  // 在生产环境中，您可以将这些指标发送到分析服务
  if (process.env.NODE_ENV === 'production') {
    // 示例：发送到 Google Analytics
    // gtag('event', metric.name, {
    //   event_category: 'Web Vitals',
    //   value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    //   event_label: metric.id,
    //   non_interaction: true,
    // });
    
    // 或者发送到其他分析服务
    console.log('Web Vitals:', metric);
  } else {
    // 开发环境中在控制台显示
    console.log('Web Vitals:', metric);
  }
}

// Google Analytics 配置
export const GA_TRACKING_ID = process.env.NEXT_PUBLIC_GA_ID || '';

// 页面浏览量跟踪
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('config', GA_TRACKING_ID, {
      page_location: url,
    });
  }
};

// 事件跟踪
export const event = ({ action, category, label, value }: {
  action: string;
  category: string;
  label?: string;
  value?: number;
}) => {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    });
  }
};

// 百度统计配置
export const BAIDU_ANALYTICS_ID = process.env.NEXT_PUBLIC_BAIDU_ID || '';

// 百度统计页面浏览量跟踪
export const baiduPageview = (url: string) => {
  if (typeof window !== 'undefined' && (window as any)._hmt) {
    (window as any)._hmt.push(['_trackPageview', url]);
  }
};

// 百度统计事件跟踪
export const baiduEvent = (category: string, action: string, label?: string) => {
  if (typeof window !== 'undefined' && (window as any)._hmt) {
    (window as any)._hmt.push(['_trackEvent', category, action, label]);
  }
};
