import { NextResponse } from 'next/server'

const newsItems = [
  {
    id: "1",
    title: "零点科技完成新一轮融资,加速AI技术创新",
    content: `
    <p>零点科技今日宣布完成新一轮融资，将进一步加大在人工智能领域的研发投入，推动技术创新和产品升级。本轮融资由多家知名投资机构参与，充分体现了资本市场对公司发展前景的认可。</p>

    <h2>融资计划</h2>
    <p>本轮融资将主要用于以下几个方面：</p>
    <ul>
      <li>加大AI算法研发投入</li>
      <li>扩充研发团队</li>
      <li>加速产品升级迭代</li>
      <li>拓展全球市场</li>
    </ul>

    <h2>未来展望</h2>
    <p>公司将继续专注于AI技术创新,为客户提供更优质的产品和服务,推动行业数字化转型。</p>
    `,
    date: "2024-03-20",
    category: "公司新闻",
    image: "https://picsum.photos/seed/news1/1200/600",
    author: {
      name: "张明",
      role: "公关总监",
      image: "https://picsum.photos/seed/author1/40/40"
    }
  },
  {
    id: "2",
    title: "零点科技荣获“2024年度最具创新力企业”奖项",
    content: `
    <p>零点科技今日宣布荣获“2024年度最具创新力企业”奖项,这是对公司在技术创新和市场应用方面的认可。</p>
    `,  
    date: "2024-03-20",
    category: "公司新闻",
    image: "https://picsum.photos/seed/news2/1200/600",
    author: {
      name: "李华",
      role: "公关总监",
      image: "https://picsum.photos/seed/author2/40/40"
    }
  }
]

export async function generateStaticParams() {
  return newsItems.map((news) => ({
    id: news.id,
  }))
}

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  const news = newsItems.find(item => item.id === params.id)
  
  if (!news) {
    return new NextResponse('News not found', { status: 404 })
  }

  return NextResponse.json(news)
} 