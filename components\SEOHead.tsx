import Head from 'next/head'
import { useRouter } from 'next/router'

interface SEOHeadProps {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  article?: boolean
  publishedTime?: string
  modifiedTime?: string
  author?: string
  noindex?: boolean
  canonical?: string
}

export function SEOHead({
  title = '零点科技 - 企业级AI与云计算解决方案提供商',
  description = '零点科技专注于AI智能标注、CPU算力租用、教育培训管理和定制软件开发，为企业提供专业的技术解决方案和服务。',
  keywords = ['零点科技', 'AI智能标注', 'CPU算力租用', '教育培训管理', '定制软件开发'],
  image = '/og-image.jpg',
  article = false,
  publishedTime,
  modifiedTime,
  author = '零点科技',
  noindex = false,
  canonical
}: SEOHeadProps) {
  const router = useRouter()
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  const currentUrl = `${baseUrl}${router.asPath}`
  const canonicalUrl = canonical || currentUrl
  const imageUrl = image.startsWith('http') ? image : `${baseUrl}${image}`

  return (
    <Head>
      {/* 基础 Meta 标签 */}
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords.join(', ')} />
      <meta name="author" content={author} />
      
      {/* Robots */}
      <meta name="robots" content={noindex ? 'noindex,nofollow' : 'index,follow'} />
      <meta name="googlebot" content={noindex ? 'noindex,nofollow' : 'index,follow'} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph */}
      <meta property="og:type" content={article ? 'article' : 'website'} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={imageUrl} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:site_name" content="零点科技" />
      <meta property="og:locale" content="zh_CN" />
      
      {/* Article specific */}
      {article && publishedTime && (
        <meta property="article:published_time" content={publishedTime} />
      )}
      {article && modifiedTime && (
        <meta property="article:modified_time" content={modifiedTime} />
      )}
      {article && (
        <meta property="article:author" content={author} />
      )}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={imageUrl} />
      <meta name="twitter:creator" content="@0dot_tech" />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=5" />
      <meta name="format-detection" content="telephone=no" />
      <meta name="format-detection" content="email=no" />
      <meta name="format-detection" content="address=no" />
      
      {/* 主题色 */}
      <meta name="theme-color" content="#3b82f6" />
      <meta name="msapplication-TileColor" content="#3b82f6" />
      
      {/* 图标 */}
      <link rel="icon" href="/favicon.ico" />
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
      <link rel="manifest" href="/manifest.json" />
      
      {/* DNS 预解析 */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      <link rel="dns-prefetch" href="//hm.baidu.com" />
      
      {/* 预连接 */}
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* 语言和地区 */}
      <meta httpEquiv="content-language" content="zh-CN" />
      <meta name="geo.region" content="CN-BJ" />
      <meta name="geo.placename" content="Beijing" />
      
      {/* 版权信息 */}
      <meta name="copyright" content="零点科技" />
      <meta name="publisher" content="零点科技" />
      
      {/* 搜索引擎验证 */}
      {process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION && (
        <meta name="google-site-verification" content={process.env.NEXT_PUBLIC_GOOGLE_VERIFICATION} />
      )}
      {process.env.NEXT_PUBLIC_BAIDU_VERIFICATION && (
        <meta name="baidu-site-verification" content={process.env.NEXT_PUBLIC_BAIDU_VERIFICATION} />
      )}
      {process.env.NEXT_PUBLIC_BING_VERIFICATION && (
        <meta name="msvalidate.01" content={process.env.NEXT_PUBLIC_BING_VERIFICATION} />
      )}
    </Head>
  )
}
