# 网站基础配置
NEXT_PUBLIC_SITE_URL=https://0dot.com
NEXT_PUBLIC_API_URL=https://api.0dot.com

# SEO 和分析
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
NEXT_PUBLIC_BAIDU_ID=xxxxxxxxxxxxxxxx

# 搜索引擎验证
NEXT_PUBLIC_GOOGLE_VERIFICATION=your-google-verification-code
NEXT_PUBLIC_BAIDU_VERIFICATION=your-baidu-verification-code
NEXT_PUBLIC_BING_VERIFICATION=your-bing-verification-code

# 数据库配置（如果需要）
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# 邮件服务配置（如果需要）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# 第三方服务 API 密钥
OPENAI_API_KEY=your-openai-api-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key

# 安全配置
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://0dot.com

# 云存储配置（如果需要）
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name

# Redis 配置（如果需要）
REDIS_URL=redis://localhost:6379

# 开发环境配置
NODE_ENV=development
