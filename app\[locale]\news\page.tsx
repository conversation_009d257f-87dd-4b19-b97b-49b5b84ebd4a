"use client"

import { motion } from "framer-motion"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Calendar, Clock, User } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

const news = [
  {
    id: "1",
    title: "零点科技AI智能标注平台服务客户突破1000家",
    summary: "零点科技AI智能标注平台凭借高精度、高效率的服务，成功服务超过1000家企业客户，累计标注数据量超过1000万条。",
    content: "随着人工智能技术的快速发展，高质量的训练数据成为AI模型成功的关键因素。零点科技AI智能标注平台自推出以来，凭借其专业的标注团队、先进的标注工具和严格的质量控制体系，赢得了众多企业客户的信赖。",
    date: "2024-03-20",
    category: "业务动态",
    image: "https://picsum.photos/seed/ai-news/800/400",
    readTime: "3分钟",
    author: "张明"
  },
  {
    id: "2",
    title: "CPU算力租用服务助力科研院所突破计算瓶颈",
    summary: "零点科技CPU算力租用服务为多家科研院所提供强大计算支持，帮助完成大规模科学计算任务，获得客户一致好评。",
    content: "科学研究往往需要大量的计算资源，传统的自建计算集群成本高昂且维护复杂。零点科技的CPU算力租用服务为科研院所提供了灵活、高效的解决方案。",
    date: "2024-03-15",
    category: "客户案例",
    image: "https://picsum.photos/seed/cpu-news/800/400",
    readTime: "4分钟",
    author: "李华"
  },
  {
    id: "3",
    title: "教育培训管理系统赋能在线教育新发展",
    summary: "零点科技教育培训管理系统成功部署于多家教育机构，支持50,000+学员在线学习，推动教育数字化转型。",
    content: "疫情加速了在线教育的发展，传统教育机构急需数字化转型。零点科技的教育培训管理系统提供了完整的在线教育解决方案。",
    date: "2024-03-10",
    category: "产品更新",
    image: "https://picsum.photos/seed/edu-news/800/400",
    readTime: "5分钟",
    author: "王芳"
  },
  {
    id: "4",
    title: "零点科技荣获ISO27001信息安全管理体系认证",
    summary: "零点科技通过ISO27001认证，进一步提升数据安全保护能力，为客户提供更加安全可靠的服务保障。",
    content: "数据安全是企业服务的重中之重。零点科技始终将客户数据安全放在首位，通过ISO27001认证标志着公司在信息安全管理方面达到了国际先进水平。",
    date: "2024-03-05",
    category: "公司新闻",
    image: "https://picsum.photos/seed/cert-news/800/400",
    readTime: "3分钟",
    author: "陈强"
  },
  {
    id: "5",
    title: "AI数据标注行业白皮书发布，零点科技贡献核心观点",
    summary: "零点科技参与编写的《2024年AI数据标注行业白皮书》正式发布，为行业发展提供重要参考和指导。",
    content: "作为AI数据标注行业的领军企业，零点科技积极参与行业标准制定和发展规划。此次白皮书的发布将为整个行业的健康发展提供重要指导。",
    date: "2024-02-28",
    category: "行业动态",
    image: "https://picsum.photos/seed/whitepaper-news/800/400",
    readTime: "6分钟",
    author: "刘敏"
  },
  {
    id: "6",
    title: "零点科技与知名高校达成产学研合作协议",
    summary: "零点科技与多所知名高校签署产学研合作协议，共同推进AI技术研发和人才培养，促进产业发展。",
    content: "产学研合作是推动技术创新和人才培养的重要途径。零点科技与高校的合作将为公司技术发展注入新的活力，同时为学生提供实践机会。",
    date: "2024-02-20",
    category: "合作伙伴",
    image: "https://picsum.photos/seed/university-news/800/400",
    readTime: "4分钟",
    author: "赵磊"
  }
]

const categories = ["全部", "业务动态", "客户案例", "产品更新", "公司新闻", "行业动态", "合作伙伴"]

export default function NewsPage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative py-24 sm:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary/10" />
        <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="text-4xl font-bold tracking-tight sm:text-6xl"
            >
              新闻中心
            </motion.h1>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="mt-6 text-lg leading-8 text-muted-foreground"
            >
              了解零点科技最新动态与行业资讯
            </motion.p>
          </div>
        </div>
      </section>

      {/* Categories Filter */}
      <section className="py-8 border-b">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="flex flex-wrap gap-2 justify-center">
            {categories.map((category) => (
              <Button
                key={category}
                variant={category === "全部" ? "default" : "outline"}
                size="sm"
                className="rounded-full"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>
      </section>

      {/* News Grid */}
      <section className="py-24 sm:py-32">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="grid gap-8 lg:grid-cols-2 xl:grid-cols-3">
            {news.map((item, index) => (
              <motion.article
                key={item.id}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="group relative overflow-hidden rounded-2xl bg-card hover:shadow-lg transition-shadow duration-300"
              >
                <div className="aspect-[16/9] relative overflow-hidden">
                  <Image
                    src={item.image}
                    alt={item.title}
                    fill
                    className="object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-background/80 backdrop-blur-sm">
                      {item.category}
                    </Badge>
                  </div>
                </div>
                
                <div className="p-6">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {item.date}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {item.readTime}
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      {item.author}
                    </div>
                  </div>
                  
                  <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors">
                    <Link href={`/news/${item.id}`} className="stretched-link">
                      {item.title}
                    </Link>
                  </h3>
                  
                  <p className="text-muted-foreground text-sm leading-relaxed mb-4 line-clamp-3">
                    {item.summary}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <Link 
                      href={`/news/${item.id}`}
                      className="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                      阅读更多
                      <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </motion.article>
            ))}
          </div>
          
          {/* Load More Button */}
          <div className="mt-16 text-center">
            <Button variant="outline" size="lg">
              加载更多新闻
            </Button>
          </div>
        </div>
      </section>

      {/* Newsletter Subscription */}
      <section className="py-24 sm:py-32 bg-muted/30">
        <div className="mx-auto max-w-7xl px-6 lg:px-8">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
              订阅我们的新闻通讯
            </h2>
            <p className="mt-6 text-lg leading-8 text-muted-foreground">
              第一时间获取零点科技最新动态和行业资讯
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button size="lg">
                立即订阅
              </Button>
              <Button variant="outline" size="lg">
                了解更多
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
