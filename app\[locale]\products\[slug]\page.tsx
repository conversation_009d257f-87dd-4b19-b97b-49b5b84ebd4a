import { notFound } from "next/navigation"
import { ProductDetail } from "./components/ProductDetail"
import { Metadata } from 'next'
import { getProductDetails } from "@/lib/products-data"
import { getTranslations } from 'next-intl/server'


type Benefit = {
  title: string;
  description: string;
}

type Product = {
  name: string;
  description: string;
  features: string[];
  techSpecs: {
    deployment: string;
    security: string;
    availability: string;
    support: string;
  };
  demoVideo?: {
    url: string;
    thumbnail?: string;
  };
  benefits?: Benefit[];
}

// Create a simple translation function for product details
const createProductTranslation = (key: string) => {
  // For product details, we'll use the Chinese keys as fallback since it's the original language
  const translations: Record<string, string> = {
    'productDetails.aiAnnotation.name': 'AI智能标注平台',
    'productDetails.cpuRental.name': 'CPU算力租用',
    'productDetails.educationManagement.name': '教育培训管理系统'
    // Add more translations as needed
  }
  return translations[key] || key
}

function getProduct(slug: string): Product | null {
  const productDetails = getProductDetails(createProductTranslation)
  const product = productDetails[slug as keyof typeof productDetails]
  return product || null
}

function getAllProductSlugs() {
  const productDetails = getProductDetails(createProductTranslation)
  return Object.keys(productDetails)
}

export function generateStaticParams() {
  const productDetails = getProductDetails(createProductTranslation)
  const slugs = Object.keys(productDetails)
  return slugs.map((slug: string) => ({
    slug: slug,
  }))
}


export async function generateMetadata(
  { params }: { params: { slug: string } }
): Promise<Metadata> {
  const t = await getTranslations()
  const productDetails = getProductDetails(t)
  const product = productDetails[params.slug as keyof typeof productDetails]
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'

  if (!product) {
    return {
      title: '产品未找到 - 零点科技',
      description: '抱歉，您访问的产品页面不存在',
    }
  }

  return {
    title: `${product.name} - 零点科技`,
    description: `${product.description} 零点科技为您提供专业的${product.name}服务，助力企业数字化转型。`,
    keywords: [
      product.name,
      '零点科技',
      ...product.features,
      product.techSpecs?.deployment,
      product.techSpecs?.security,
    ].filter(Boolean),
    openGraph: {
      title: `${product.name} - 零点科技`,
      description: product.description,
      url: `${baseUrl}/products/${params.slug}`,
      siteName: '零点科技',
      images: [
        {
          url: `${baseUrl}/products/${params.slug}/og-image.jpg`,
          width: 1200,
          height: 630,
          alt: `${product.name} - 零点科技`,
        },
      ],
      locale: 'zh_CN',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${product.name} - 零点科技`,
      description: product.description,
      images: [`${baseUrl}/products/${params.slug}/og-image.jpg`],
    },
    alternates: {
      canonical: `${baseUrl}/products/${params.slug}`,
    },
  }
}

export default async function ProductPage({ params }: { params: { slug: string } }) {
  const t = await getTranslations()
  const productDetails = getProductDetails(t)
  const product = productDetails[params.slug as keyof typeof productDetails]

  if (!product) {
    notFound()
  }

  return (
    <>
      <ProductDetail product={product} />
    </>
  )
}