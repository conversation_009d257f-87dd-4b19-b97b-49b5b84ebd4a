'use client'

import Image from 'next/image'
import React from 'react'
import { motion } from "framer-motion"
import { useTranslations } from '@/hooks/useTranslations'

function TestimonialsSection() {
  const t = useTranslations('home.testimonials')

  const testimonials = [
    {
      content: t('items.0.content'),
      author: t('items.0.author'),
      role: t('items.0.role'),
      company: t('items.0.company'),
      image: "https://picsum.photos/seed/user1/200/200"
    },
    {
      content: t('items.1.content'),
      author: t('items.1.author'),
      role: t('items.1.role'),
      company: t('items.1.company'),
      image: "https://picsum.photos/seed/user2/200/200"
    },
    {
      content: t('items.2.content'),
      author: t('items.2.author'),
      role: t('items.2.role'),
      company: t('items.2.company'),
      image: "https://picsum.photos/seed/user3/200/200"
    }
  ]

  return (
    <div className="py-24 sm:py-32 bg-slate-50/50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-xl text-center">
          <h2 className="text-lg font-semibold leading-8" style={{ color: 'rgb(59 130 246)' }}>{t('title')}</h2>
          <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl text-gradient-modern">
            {t('subtitle')}
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:mx-0 lg:max-w-none lg:grid-cols-3">
          {testimonials.map((testimonial, index) => (
            <motion.figure
              key={testimonial.author}
              className="card-modern p-8 group hover-glow"
              style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="relative">
                <div className="absolute -top-2 -left-2 text-6xl font-serif" style={{ color: 'rgba(59, 130, 246, 0.2)' }}>"</div>
                <blockquote className="text-lg font-medium leading-relaxed relative z-10 mb-6">
                  <p className="text-slate-700">
                    {testimonial.content}
                  </p>
                </blockquote>
              </div>
              <figcaption className="flex items-center gap-x-4">
                <div className="relative">
                  <Image
                    className="h-12 w-12 rounded-full ring-2 ring-blue-200 group-hover:ring-blue-400 transition-all duration-300"
                    src={testimonial.image}
                    alt=""
                    width={48}
                    height={48}
                  />
                  <div className="absolute inset-0 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'rgba(59, 130, 246, 0.1)' }} />
                </div>
                <div>
                  <div className="font-semibold text-slate-800">
                    {testimonial.author}
                  </div>
                  <div className="text-sm text-slate-500">
                    {testimonial.role}，{testimonial.company}
                  </div>
                </div>
              </figcaption>
              <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235), rgb(29 78 216))' }} />
            </motion.figure>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TestimonialsSection