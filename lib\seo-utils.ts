// 服务器端SEO工具函数

import { getProducts } from './products-data'

// Create a simple translation function for SEO utils
const createSeoTranslation = (key: string) => {
  // For SEO, we'll use the Chinese keys as fallback since it's the original language
  const translations: Record<string, string> = {
    'products.aiAnnotation.name': 'AI智能标注平台',
    'products.aiAnnotation.description': '专业的AI数据标注服务，支持图像、文本、语音等多模态数据标注，为机器学习模型提供高质量训练数据',
    'products.aiAnnotation.category': 'AI服务',
    'products.cpuRental.name': 'CPU算力租用',
    'products.cpuRental.description': '灵活的云计算资源租用服务，提供高性能CPU集群，支持科学计算、深度学习训练等高算力需求',
    'products.cpuRental.category': '云计算',
    'products.educationManagement.name': '教育培训管理系统',
    'products.educationManagement.description': '一站式教育培训管理平台，涵盖课程管理、学员管理、在线考试、证书颁发等完整教育生态',
    'products.educationManagement.category': '教育科技',
    'products.dataAnnotationPro.name': '专业数据标注',
    'products.dataAnnotationPro.description': '面向企业级客户的高精度数据标注服务，提供定制化标注方案和专业质检团队',
    'products.dataAnnotationPro.category': 'AI服务',
    'products.customWebDevelopment.name': '定制Web开发',
    'products.customWebDevelopment.description': '企业级Web应用开发服务，提供前后端全栈开发、系统架构设计和技术咨询',
    'products.customWebDevelopment.category': '定制开发',
    'products.mobileAppDevelopment.name': '移动应用开发',
    'products.mobileAppDevelopment.description': 'iOS和Android原生应用开发，以及跨平台移动应用解决方案',
    'products.mobileAppDevelopment.category': '定制开发',
    'products.enterpriseSoftware.name': '企业软件定制',
    'products.enterpriseSoftware.description': '面向企业的定制化软件解决方案，包括ERP、CRM、OA等管理系统',
    'products.enterpriseSoftware.category': '定制开发'
  }
  return translations[key] || key
}

interface BreadcrumbItem {
  label: string
  href: string
}

// 生成结构化数据的面包屑（服务器端版本）
export function generateBreadcrumbStructuredData(items: BreadcrumbItem[]) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.label,
      "item": `${baseUrl}${item.href}`
    }))
  }
}

// 生成产品结构化数据
export function generateProductStructuredData(product: any, slug: string) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "url": `${baseUrl}/products/${slug}`,
    "brand": {
      "@type": "Brand",
      "name": "零点科技"
    },
    "manufacturer": {
      "@type": "Organization",
      "name": "零点科技"
    },
    "offers": {
      "@type": "Offer",
      "availability": "https://schema.org/InStock",
      "seller": {
        "@type": "Organization",
        "name": "零点科技"
      }
    },
    "additionalProperty": product.features?.map((feature: string) => ({
      "@type": "PropertyValue",
      "name": "功能特性",
      "value": feature
    }))
  }
}

// 生成产品列表结构化数据
export function generateProductListStructuredData(products: any[]) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    "name": "产品服务",
    "description": "零点科技提供AI智能标注、CPU算力租用、教育培训管理和定制软件开发等专业服务",
    "url": `${baseUrl}/products`,
    "mainEntity": {
      "@type": "ItemList",
      "numberOfItems": getProducts(createSeoTranslation).length,
    "itemListElement": getProducts(createSeoTranslation).map((product, index) => ({
        "@type": "Product",
        "position": index + 1,
        "name": product.name,
        "description": product.description,
        "category": product.category,
        "url": `${baseUrl}/products/${product.slug}`
      }))
    }
  }
}

// 生成新闻文章结构化数据
export function generateNewsStructuredData(news: any) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  return {
    "@context": "https://schema.org",
    "@type": "NewsArticle",
    "headline": news.title,
    "description": news.summary || news.content?.substring(0, 160),
    "url": `${baseUrl}/news/${news.id}`,
    "datePublished": news.date,
    "dateModified": news.date,
    "author": {
      "@type": "Person",
      "name": news.author?.name || news.author
    },
    "publisher": {
      "@type": "Organization",
      "name": "零点科技",
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`
      }
    },
    "image": news.image,
    "articleSection": news.category
  }
}

// 生成页面结构化数据
export function generatePageStructuredData(page: {
  name: string
  description: string
  url: string
  breadcrumbs?: BreadcrumbItem[]
}) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://0dot.com'
  
  const structuredData: any = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": page.name,
    "description": page.description,
    "url": page.url,
    "mainEntity": {
      "@type": "Organization",
      "name": "零点科技",
      "description": "企业级AI与云计算解决方案提供商"
    }
  }
  
  if (page.breadcrumbs) {
    structuredData.breadcrumb = generateBreadcrumbStructuredData(page.breadcrumbs)
  }
  
  return structuredData
}
