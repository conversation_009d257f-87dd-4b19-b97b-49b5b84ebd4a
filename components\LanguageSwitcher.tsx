'use client'

import { useRouter, usePathname } from 'next/navigation'
import { useLocale, useTranslations } from '@/hooks/useTranslations'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Globe, Check } from 'lucide-react'
import { useState, useTransition } from 'react'

const languages = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'en', name: 'English', flag: '🇺🇸' }
]

export function LanguageSwitcher() {
  const router = useRouter()
  const pathname = usePathname()
  const locale = useLocale()
  const t = useTranslations('common')
  const [isPending, startTransition] = useTransition()

  const handleLanguageChange = (newLocale: string) => {
    startTransition(() => {
      // Remove the current locale from the pathname
      let pathWithoutLocale = pathname
      if (pathname.startsWith(`/${locale}`)) {
        pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/'
      }

      // Add the new locale to the pathname
      const newPath = `/${newLocale}${pathWithoutLocale}`

      router.push(newPath)
    })
  }

  const currentLanguage = languages.find(lang => lang.code === locale)

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="ghost" 
          size="sm" 
          className="h-8 w-8 px-0"
          disabled={isPending}
        >
          <Globe className="h-4 w-4" />
          <span className="sr-only">{t('language')}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center gap-2">
              <span className="text-base">{language.flag}</span>
              <span className="text-sm">{language.name}</span>
            </div>
            {locale === language.code && (
              <Check className="h-4 w-4 text-primary" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// 移动端语言切换器
export function MobileLanguageSwitcher() {
  const router = useRouter()
  const pathname = usePathname()
  const locale = useLocale()
  const t = useTranslations('common')
  const [isPending, startTransition] = useTransition()

  const handleLanguageChange = (newLocale: string) => {
    startTransition(() => {
      let pathWithoutLocale = pathname
      if (pathname.startsWith(`/${locale}`)) {
        pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/'
      }
      const newPath = `/${newLocale}${pathWithoutLocale}`
      router.push(newPath)
    })
  }

  return (
    <div className="border-t pt-4 mt-4">
      <div className="px-2 py-1 text-sm font-medium text-muted-foreground">
        {t('language')}
      </div>
      <div className="space-y-1">
        {languages.map((language) => (
          <button
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            disabled={isPending}
            className={`w-full flex items-center justify-between px-2 py-2 text-sm rounded-md transition-colors ${
              locale === language.code
                ? 'bg-primary/10 text-primary'
                : 'text-muted-foreground hover:bg-muted hover:text-foreground'
            }`}
          >
            <div className="flex items-center gap-2">
              <span className="text-base">{language.flag}</span>
              <span>{language.name}</span>
            </div>
            {locale === language.code && (
              <Check className="h-4 w-4" />
            )}
          </button>
        ))}
      </div>
    </div>
  )
}
