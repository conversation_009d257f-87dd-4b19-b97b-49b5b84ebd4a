import { NextResponse } from 'next/server'

interface ContactFormData {
  name: string
  email: string
  subject: string
  message: string
  marketing: boolean
}

export async function POST(request: Request) {
  try {
    const data: ContactFormData = await request.json()
    
    // 验证必填字段
    if (!data.name || !data.email || !data.message) {
      return new NextResponse('Missing required fields', { status: 400 })
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return new NextResponse('Invalid email format', { status: 400 })
    }

    // 验证营销订阅
    if (!data.marketing) {
      return new NextResponse('Marketing consent required', { status: 400 })
    }

    // TODO: 这里可以添加实际的业务逻辑
    // 例如：发送邮件通知、保存到数据库等
    
    // 模拟异步操作
    await new Promise(resolve => setTimeout(resolve, 1000))

    return NextResponse.json({
      message: '消息已发送，我们会尽快回复您！'
    })

  } catch (error) {
    console.error('Contact form submission error:', error)
    return new NextResponse('Internal server error', { status: 500 })
  }
} 