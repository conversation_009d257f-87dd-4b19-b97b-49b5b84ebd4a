'use client'

import Image from 'next/image'
import React from 'react'
import { motion } from "framer-motion"
import { useRawTranslations } from '@/hooks/useTranslations'

function SolutionsSection() {
  const rawData = useRawTranslations('home.solutionsSection')

  const solutions = rawData.solutions || [
    {
      title: 'AI数据标注',
      description: '专业的AI数据标注服务，支持图像、文本、语音等多模态数据，为机器学习提供高质量训练数据。',
      image: 'https://picsum.photos/seed/ai-annotation/800/600',
      features: ['图像识别标注', '自然语言处理', '语音识别标注', '视频内容标注']
    },
    {
      title: '云计算资源',
      description: '提供高性能CPU集群租用服务，支持科学计算、深度学习训练等高算力需求场景。',
      image: 'https://picsum.photos/seed/cloud-computing/800/600',
      features: ['弹性扩容', '按需付费', '高可用性', '安全可靠']
    },
    {
      title: '教育管理平台',
      description: '全方位的教育培训管理解决方案，涵盖课程管理、在线考试、学员跟踪等完整功能。',
      image: 'https://picsum.photos/seed/education-platform/800/600',
      features: ['课程管理', '在线考试', '学员管理', '数据分析']
    }
  ]

  return (
    <div className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl lg:text-center">
          <h2 className="text-base font-semibold leading-7" style={{ color: 'rgb(59 130 246)' }}>
            {rawData.sectionTitle || '行业赋能'}
          </h2>
          <p className="mt-2 text-3xl font-bold tracking-tight sm:text-4xl text-gradient-modern">
            {rawData.title || '定制化解决方案'}
          </p>
          <p className="mt-6 text-lg leading-8 text-slate-600">
            {rawData.subtitle || '通过跨界融合与协同创新，为多个行业提供智能化解决方案，助力传统产业升级转型。'}
          </p>
        </div>
        <div className="mx-auto mt-16 grid max-w-2xl grid-cols-1 gap-8 lg:max-w-none lg:grid-cols-3">
          {solutions.map((solution: any, index: number) => (
            <motion.div
              key={solution.title}
              className="relative overflow-hidden rounded-2xl group"
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="aspect-[4/3] relative">
                <Image
                  src={solution.image}
                  alt={solution.title}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-black/0" />
              </div>
              <div className="absolute bottom-0 p-6 text-white">
                <h3 className="text-xl font-semibold mb-2">{solution.title}</h3>
                <p className="text-sm text-white/80 mb-4">{solution.description}</p>
                <div className="flex flex-wrap gap-2">
                  {solution.features.map((feature: string) => (
                    <span
                      key={feature}
                      className="inline-flex items-center rounded-full bg-white/20 backdrop-blur-sm px-2 py-1 text-xs font-medium text-white"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </div>
  )
}


export default SolutionsSection