'use client'

import React from 'react'
import { motion } from "framer-motion"
import { Code, Database, Cloud, Shield, Zap, Cpu, Brain, Globe } from 'lucide-react'

function TechnologyStack() {
  const techCategories = [
    {
      category: '前端技术',
      icon: Code,
      color: 'from-blue-500 to-blue-600',
      technologies: [
        { name: 'React', level: 95, description: '现代化用户界面' },
        { name: 'Next.js', level: 90, description: '全栈框架' },
        { name: 'TypeScript', level: 88, description: '类型安全' },
        { name: 'Tailwind CSS', level: 92, description: '快速样式开发' }
      ]
    },
    {
      category: '后端技术',
      icon: Database,
      color: 'from-green-500 to-green-600',
      technologies: [
        { name: 'Node.js', level: 90, description: '高性能服务器' },
        { name: 'Python', level: 95, description: 'AI/ML开发' },
        { name: 'PostgreSQL', level: 85, description: '关系型数据库' },
        { name: 'Redis', level: 80, description: '缓存系统' }
      ]
    },
    {
      category: '云计算',
      icon: Cloud,
      color: 'from-purple-500 to-purple-600',
      technologies: [
        { name: 'AWS', level: 88, description: '云服务平台' },
        { name: 'Docker', level: 85, description: '容器化部署' },
        { name: 'Kubernetes', level: 82, description: '容器编排' },
        { name: 'Terraform', level: 78, description: '基础设施即代码' }
      ]
    },
    {
      category: 'AI/ML',
      icon: Brain,
      color: 'from-orange-500 to-orange-600',
      technologies: [
        { name: 'TensorFlow', level: 90, description: '深度学习框架' },
        { name: 'PyTorch', level: 88, description: '机器学习' },
        { name: 'OpenCV', level: 85, description: '计算机视觉' },
        { name: 'Scikit-learn', level: 82, description: '机器学习库' }
      ]
    }
  ]

  const highlights = [
    {
      icon: Zap,
      title: '高性能',
      description: '优化的架构设计，确保系统高效运行',
      color: 'text-yellow-600'
    },
    {
      icon: Shield,
      title: '安全可靠',
      description: '企业级安全标准，保障数据安全',
      color: 'text-green-600'
    },
    {
      icon: Globe,
      title: '全球部署',
      description: '支持全球多地区部署，就近服务',
      color: 'text-blue-600'
    },
    {
      icon: Cpu,
      title: '智能优化',
      description: 'AI驱动的自动化优化和监控',
      color: 'text-purple-600'
    }
  ]

  return (
    <section className="py-24 sm:py-32 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-b from-slate-50 via-white to-blue-50/30" />
      <div className="absolute inset-0 grid-bg opacity-20" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题区域 */}
        <motion.div
          className="mx-auto max-w-3xl text-center mb-20"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-lg font-semibold leading-7 mb-4 text-blue-600">技术架构</h2>
          <p className="text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl text-gradient-modern mb-8">
            先进的技术栈
          </p>
          <p className="text-xl leading-relaxed text-slate-600">
            采用业界领先的技术栈，构建稳定、高效、可扩展的系统架构
          </p>
        </motion.div>

        {/* 技术栈网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-20">
          {techCategories.map((category, index) => (
            <motion.div
              key={category.category}
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <TechCategoryCard category={category} />
            </motion.div>
          ))}
        </div>

        {/* 技术亮点 */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          {highlights.map((highlight, index) => (
            <div key={highlight.title} className="text-center group">
              <div className="flex justify-center mb-4">
                <div className="p-4 rounded-2xl bg-white shadow-lg group-hover:shadow-xl transition-all duration-300 hover-lift">
                  <highlight.icon className={`h-8 w-8 ${highlight.color}`} />
                </div>
              </div>
              <h3 className="text-lg font-semibold text-slate-800 mb-2">{highlight.title}</h3>
              <p className="text-sm text-slate-600">{highlight.description}</p>
            </div>
          ))}
        </motion.div>
      </div>
    </section>
  )
}

// 技术分类卡片组件
function TechCategoryCard({ category }: { category: any }) {
  return (
    <div className="card-modern p-8 h-full hover-glow group">
      {/* 分类标题 */}
      <div className="flex items-center gap-4 mb-8">
        <div className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br ${category.color} shadow-lg group-hover:scale-110 transition-transform duration-300`}>
          <category.icon className="h-6 w-6 text-white" />
        </div>
        <h3 className="text-xl font-bold text-slate-800 group-hover:text-gradient-modern transition-all duration-300">
          {category.category}
        </h3>
      </div>

      {/* 技术列表 */}
      <div className="space-y-6">
        {category.technologies.map((tech: any, index: number) => (
          <motion.div
            key={tech.name}
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true }}
            className="group/tech"
          >
            <div className="flex items-center justify-between mb-2">
              <div>
                <span className="font-medium text-slate-800 group-hover/tech:text-blue-600 transition-colors duration-200">
                  {tech.name}
                </span>
                <p className="text-sm text-slate-500">{tech.description}</p>
              </div>
              <span className="text-sm font-medium text-blue-600">{tech.level}%</span>
            </div>
            
            {/* 进度条 */}
            <div className="w-full bg-slate-100 rounded-full h-2 overflow-hidden">
              <motion.div
                className="h-full bg-gradient-to-r from-blue-500 to-blue-600 rounded-full"
                initial={{ width: 0 }}
                whileInView={{ width: `${tech.level}%` }}
                transition={{ duration: 1, delay: index * 0.1 + 0.5 }}
                viewport={{ once: true }}
              />
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}

export default TechnologyStack
