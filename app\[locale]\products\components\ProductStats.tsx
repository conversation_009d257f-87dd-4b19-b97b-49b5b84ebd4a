'use client'

import { motion } from "framer-motion"
import { TrendingUp, Users, Database, Award } from "lucide-react"
import { useTranslations } from "@/hooks/useTranslations"

export function ProductStats() {
  const t = useTranslations('products.productStats')
  
  const stats = [
    {
      icon: Database,
      label: t('dataProcessing.label'),
      value: "10TB+",
      description: t('dataProcessing.description'),
      trend: "+25%"
    },
    {
      icon: Users,
      label: t('customers.label'),
      value: "2000+",
      description: t('customers.description'),
      trend: "+40%"
    },
    {
      icon: TrendingUp,
      label: t('availability.label'),
      value: "99.9%",
      description: t('availability.description'),
      trend: t('availability.trend')
    },
    {
      icon: Award,
      label: t('certifications.label'),
      value: "15+",
      description: t('certifications.description'),
      trend: "+3"
    }
  ]
  return (
    <section className="py-24 sm:py-32 relative">
      {/* 背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/30" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        {/* 标题 */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl text-gradient-modern mb-4">
            {t('title')}
          </h2>
          <p className="text-lg text-slate-600 max-w-2xl mx-auto">
            {t('subtitle')}
          </p>
        </motion.div>

        {/* 统计数据网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <motion.div
              key={stat.label}
              className="group relative"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <div className="card-modern p-8 text-center hover-glow" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
                {/* 图标 */}
                <div className="relative mx-auto w-14 h-14 mb-6">
                  <div className="absolute inset-0 rounded-xl opacity-20 group-hover:opacity-30 transition-opacity duration-300" style={{ background: 'linear-gradient(135deg, rgb(59 130 246), rgb(37 99 235))' }} />
                  <div className="relative flex items-center justify-center w-full h-full rounded-xl bg-white shadow-md group-hover:scale-110 transition-transform duration-300">
                    <stat.icon className="w-7 h-7" style={{ color: 'rgb(59 130 246)' }} />
                  </div>
                </div>

                {/* 数值 */}
                <div className="mb-4">
                  <div className="text-3xl lg:text-4xl font-bold text-gradient-modern mb-2 group-hover:scale-105 transition-transform duration-300">
                    {stat.value}
                  </div>
                  <div className="text-sm font-medium text-slate-800 mb-1">
                    {stat.label}
                  </div>
                  <div className="text-xs text-slate-500">
                    {stat.description}
                  </div>
                </div>

                {/* 趋势指示器 */}
                <div className="flex items-center justify-center gap-1">
                  <TrendingUp className="w-3 h-3" style={{ color: 'rgb(59 130 246)' }} />
                  <span className="text-xs font-medium" style={{ color: 'rgb(59 130 246)' }}>
                    {stat.trend}
                  </span>
                </div>

                {/* 底部装饰线 */}
                <div className="absolute bottom-0 left-0 right-0 h-1 rounded-b-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ background: 'linear-gradient(90deg, rgb(59 130 246), rgb(37 99 235))' }} />
              </div>
            </motion.div>
          ))}
        </div>

        {/* 底部说明 */}
        <motion.div
          className="mt-16 text-center"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="inline-flex items-center gap-3 px-6 py-4 rounded-2xl bg-white/80 backdrop-blur-sm border shadow-lg" style={{ borderColor: 'rgba(59, 130, 246, 0.1)' }}>
            <div className="flex -space-x-2">
              {[1, 2, 3, 4].map((i) => (
                <div
                  key={i}
                  className="w-8 h-8 rounded-full border-2 border-white"
                  style={{ 
                    background: `linear-gradient(${45 + i * 30}deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.6))` 
                  }}
                />
              ))}
            </div>
            <div className="text-sm">
              <span className="font-medium text-slate-700">
                {t('bottomText.main')}
              </span>
              <div className="text-xs text-slate-500">
                {t('bottomText.sub')}
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
