import { NextResponse } from 'next/server'
import { getProductDetails } from "@/lib/products-data"

// Create a simple translation function for API
const createApiTranslation = (key: string) => {
  // For API, we'll use the Chinese keys as fallback since it's the original language
  const translations: Record<string, string> = {
    'productDetails.aiAnnotation.name': 'AI智能标注平台',
    'productDetails.cpuRental.name': 'CPU算力租用',
    'productDetails.educationManagement.name': '教育培训管理系统'
    // Add more translations as needed
  }
  return translations[key] || key
}

const getProductDetailsData = () => getProductDetails(createApiTranslation)

// Remove the hardcoded products object - now using getProductDetails function




export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  const productDetails = getProductDetailsData()
  const product = productDetails[params.slug as keyof typeof productDetails]
  
  if (!product) {
    return new NextResponse('Product not found', { status: 404 })
  }

  return NextResponse.json(product)
}

export function generateStaticParams() {
  const productDetails = getProductDetailsData()
  return Object.keys(productDetails).map((slug) => ({
    slug: slug,
  }))
}

