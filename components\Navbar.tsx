"use client"

import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import Link from 'next/link'
import { usePathname, useRouter } from 'next/navigation'
import { useTranslations } from '@/hooks/useTranslations'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Menu, X, CircleDot } from 'lucide-react'
import { motion, AnimatePresence, useScroll, useTransform } from 'framer-motion'
import { LanguageSwitcher, MobileLanguageSwitcher } from '@/components/LanguageSwitcher'

// 导航配置类型定义
interface NavigationItem {
  key: string
  href: string
}

// 将导航配置移到组件外部
const navigation: readonly NavigationItem[] = [
  { key: 'home', href: '/' },
  { key: 'products', href: '/products' },
  { key: 'cases', href: '/cases' },
  { key: 'about', href: '/about' },
] as const

export default function Navbar() {
  const router = useRouter()
  const pathname = usePathname()
  const t = useTranslations('navigation')
  const tCommon = useTranslations('common')
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const navRef = useRef<HTMLElement>(null)

  // 使用 framer-motion 的 useScroll 获取更流畅的滚动效果
  const { scrollY } = useScroll()
  const headerOpacity = useTransform(scrollY, [0, 100], [0.8, 0.95])
  const headerBlur = useTransform(scrollY, [0, 100], [8, 20])

  // 优化滚动处理函数
  const handleScroll = useCallback(() => {
    const shouldBeScrolled = window.scrollY > 10;
    if (scrolled !== shouldBeScrolled) {
      setScrolled(shouldBeScrolled);
    }
  }, [scrolled]);

  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // 键盘导航支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && mobileMenuOpen) {
        setMobileMenuOpen(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [mobileMenuOpen]);

  // 缓存导航项的激活状态判断
  const isActiveLink = useCallback((href: string) => pathname === href, [pathname]);

  // 增强的样式类
  const headerClasses = useMemo(() => cn(
    "fixed inset-x-0 top-0 z-50 transition-all duration-700 ease-out",
    scrolled
      ? "backdrop-blur-xl bg-background/80 border-b border-border/50 shadow-lg shadow-primary/5"
      : "bg-transparent"
  ), [scrolled]);

  // 缓存按钮点击处理函数
  const handleContactClick = useCallback(() => {
    router.push('/contact-us');
  }, [router]);

  const handleMobileMenuClose = useCallback(() => {
    setMobileMenuOpen(false);
  }, []);

  const handleMobileMenuToggle = useCallback(() => {
    setMobileMenuOpen(prev => !prev);
  }, []);

  // 增强的动画配置
  const animations = useMemo(() => ({
    logo: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      transition: { duration: 0.6, ease: "easeOut" }
    },
    mobileMenu: {
      initial: { opacity: 0, x: "100%" },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: "100%" },
      transition: { type: "spring", bounce: 0.1, duration: 0.5 }
    },
    navItem: {
      initial: { opacity: 0, y: -10 },
      animate: { opacity: 1, y: 0 },
      transition: { duration: 0.4, ease: "easeOut" }
    },
    mobileNavItem: {
      initial: { opacity: 0, x: -20 },
      animate: { opacity: 1, x: 0 },
      exit: { opacity: 0, x: -20 },
      transition: { duration: 0.3 }
    }
  }), []);

  return (
    <motion.header
      ref={navRef}
      className={headerClasses}
      style={{
        backdropFilter: scrolled ? `blur(${headerBlur}px)` : 'none',
        backgroundColor: scrolled ? `hsl(var(--background) / ${headerOpacity})` : 'transparent'
      }}
    >
      <nav
        className="flex items-center justify-between p-4 sm:p-6 lg:px-8 max-w-7xl mx-auto"
        aria-label="Global navigation"
        role="navigation"
      >
        {/* Logo Section */}
        <motion.div
          className="flex lg:flex-1"
          {...animations.logo}
        >
          <Link
            href="/"
            className="-m-1.5 p-1.5 flex items-center gap-3 group focus:outline-none focus:ring-2 focus:ring-primary/50 rounded-lg"
            aria-label="0dot 主页"
          >
            <div className="relative">
              <CircleDot className="h-8 w-8 text-indigo-600 transition-all duration-300 group-hover:scale-110 group-focus:scale-110" />
              <div className="absolute inset-0 bg-indigo-500/20 blur-xl rounded-full opacity-0 group-hover:opacity-100 group-focus:opacity-100 transition-opacity duration-300" />
            </div>
            <span className="font-bold text-xl bg-gradient-to-r from-indigo-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
              0dot
            </span>
          </Link>
        </motion.div>

        {/* Mobile Menu Button */}
        <div className="flex lg:hidden">
          <Button
            variant="ghost"
            size="icon"
            className="relative -m-2.5 p-2.5 hover:bg-primary/10 focus:bg-primary/10 transition-colors duration-200"
            onClick={handleMobileMenuToggle}
            aria-label={mobileMenuOpen ? "关闭菜单" : "打开菜单"}
            aria-expanded={mobileMenuOpen}
          >
            <motion.div
              animate={{ rotate: mobileMenuOpen ? 90 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <Menu className="h-6 w-6 text-primary" aria-hidden="true" />
            </motion.div>
          </Button>
        </div>

        {/* Desktop Navigation */}
        <div className="hidden lg:flex lg:gap-x-8 xl:gap-x-12">
          {navigation.map((item, i) => (
            <motion.div
              key={item.key}
              {...animations.navItem}
              transition={{ ...animations.navItem.transition, delay: i * 0.1 }}
            >
              <Link
                href={item.href}
                className={cn(
                  'relative px-3 py-2 text-sm font-semibold leading-6 transition-all duration-300 rounded-lg group',
                  'hover:text-primary hover:bg-primary/5 focus:outline-none focus:ring-2 focus:ring-primary/50',
                  isActiveLink(item.href)
                    ? 'text-primary bg-primary/10'
                    : 'text-muted-foreground'
                )}
                aria-label={t(item.key)}
              >
                {t(item.key)}
                <motion.span
                  className="absolute -bottom-1 left-1/2 h-0.5 bg-gradient-to-r from-primary to-primary/60 rounded-full"
                  initial={{ width: 0, x: '-50%' }}
                  animate={{
                    width: isActiveLink(item.href) ? '80%' : 0,
                    x: '-50%'
                  }}
                  whileHover={{ width: '80%' }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                />
              </Link>
            </motion.div>
          ))}
          <LanguageSwitcher />
        </div>

        {/* Desktop CTA Button */}
        <motion.div
          className="hidden lg:flex lg:flex-1 lg:justify-end"
          {...animations.logo}
        >
          <Button
            className="relative bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 hover:scale-105 focus:scale-105 shadow-lg hover:shadow-glow focus:shadow-glow"
            onClick={handleContactClick}
            aria-label={tCommon('contactUs')}
          >
            <span className="relative z-10">{tCommon('contactUs')}</span>
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-indigo-500/20 to-purple-500/10 rounded-md blur-lg"
              initial={{ opacity: 0, scale: 0.8 }}
              whileHover={{ opacity: 1, scale: 1.1 }}
              transition={{ duration: 0.3 }}
            />
          </Button>
        </motion.div>
      </nav>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm lg:hidden"
              onClick={handleMobileMenuClose}
            />

            {/* Mobile Menu Panel */}
            <motion.div
              {...animations.mobileMenu}
              className="fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-background/95 backdrop-blur-xl px-6 py-6 sm:max-w-sm border-l border-border/50 shadow-2xl"
            >
              {/* Mobile Menu Header */}
              <div className="flex items-center justify-between">
                <Link
                  href="/"
                  className="-m-1.5 p-1.5 flex items-center gap-3 group focus:outline-none focus:ring-2 focus:ring-primary/50 rounded-lg"
                  onClick={handleMobileMenuClose}
                  aria-label="0dot 主页"
                >
                  <CircleDot className="h-8 w-8 text-indigo-600 transition-transform duration-300 group-hover:scale-110" />
                  <span className="font-bold text-xl bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                    0dot
                  </span>
                </Link>
                <Button
                  variant="ghost"
                  size="icon"
                  className="-m-2.5 p-2.5 hover:bg-primary/10 focus:bg-primary/10 transition-colors duration-200"
                  onClick={handleMobileMenuClose}
                  aria-label="关闭菜单"
                >
                  <motion.div
                    animate={{ rotate: 180 }}
                    transition={{ duration: 0.3 }}
                  >
                    <X className="h-6 w-6 text-primary" aria-hidden="true" />
                  </motion.div>
                </Button>
              </div>

              {/* Mobile Navigation */}
              <div className="mt-8 flow-root">
                <div className="-my-6 divide-y divide-border/20">
                  <div className="space-y-3 py-6">
                    {navigation.map((item, index) => (
                      <motion.div
                        key={item.key}
                        {...animations.mobileNavItem}
                        transition={{ ...animations.mobileNavItem.transition, delay: index * 0.1 }}
                      >
                        <Link
                          href={item.href}
                          className={cn(
                            'group -mx-3 flex items-center gap-3 rounded-xl px-4 py-3 text-base font-semibold leading-7 transition-all duration-300',
                            'hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-primary/50',
                            pathname === item.href
                              ? 'text-primary bg-primary/15 shadow-sm'
                              : 'text-muted-foreground hover:text-primary hover:bg-primary/8'
                          )}
                          onClick={handleMobileMenuClose}
                          aria-label={t(item.key)}
                        >
                          <motion.span
                            className="w-2 h-2 rounded-full bg-primary/60"
                            whileHover={{ scale: 1.5 }}
                            transition={{ duration: 0.2 }}
                          />
                          <span>{t(item.key)}</span>
                          {pathname === item.href && (
                            <motion.div
                              className="ml-auto w-2 h-2 rounded-full bg-primary"
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              transition={{ duration: 0.3 }}
                            />
                          )}
                        </Link>
                      </motion.div>
                    ))}
                  </div>

                  {/* Mobile Language Switcher */}
                  <MobileLanguageSwitcher />

                  {/* Mobile CTA */}
                  <div className="py-6">
                    <Button
                      className="w-full bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-glow transition-all duration-300 hover:scale-[1.02]"
                      onClick={handleContactClick}
                      aria-label={tCommon('contactUs')}
                    >
                      <span>{tCommon('contactUs')}</span>
                    </Button>
                  </div>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </motion.header>
  )
}